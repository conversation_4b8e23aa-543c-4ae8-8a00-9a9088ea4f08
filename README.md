# Wildberries广告关键词效益分析系统

## 项目简介

这是一个自动化的Wildberries广告关键词分析系统，用于分析广告活动中关键词的效益，并自动决定关键词的处理策略（保持不变、固定关键词、排除关键词）。

## 功能特性

- 🔍 **自动获取广告活动**: 通过Wildberries API获取所有广告活动
- 🎯 **智能筛选**: 自动筛选拍卖广告（type=9, status=9）
- 📊 **关键词分析**: 获取关键词数据和统计信息
- 🗄️ **数据库集成**: 从PostgreSQL数据库获取关键词相关度数据
- 📈 **效益评估**: 基于多维度指标计算关键词效益得分
- 🏷️ **智能标记**: 自动标记关键词状态（排除、观察、优化、保持）
- 📋 **自动分类**: 将关键词分为高效益、中等效益、低效益三类
- 📄 **报告生成**: 生成CSV、Excel和JSON格式的分析报告
- 🔄 **多账号支持**: 支持多个API密钥的负载均衡
- ⚙️ **规则配置**: 支持通过配置文件自定义分析规则阈值

## 系统架构

```
wb_adv/
├── config.py              # 配置管理
├── models.py              # 数据模型
├── logger.py              # 日志配置
├── wb_api_client.py       # Wildberries API客户端
├── database.py            # 数据库操作
├── data_processor.py      # 数据处理和分析
├── main.py               # 主程序
├── test_connections.py   # 连接测试
├── requirements.txt      # 依赖包
├── .env                 # 环境变量配置
└── README.md            # 项目说明
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件并配置以下变量：

```env
# Wildberries API密钥（多个密钥用逗号分隔）
WB_API_KEYS=your_api_key_1,your_api_key_2

# PostgreSQL数据库配置
PG_HOST=your_db_host
PG_PORT=5432
PG_USER=your_db_user
PG_PASSWORD=your_db_password
PG_DB=your_db_name

# 数据分析规则配置（可选，有默认值）
EXCLUDE_IRRELEVANT_SIMILARITY_THRESHOLD=25
EXCLUDE_ZEROCLICK_MIN_SUM=50
EXCLUDE_NODATA_MIN_SUM=20
OBSERVE_LOWCTR_MIN_VIEWS=500
OBSERVE_LOWCTR_CTR_THRESHOLD=0.02
OBSERVE_HIGHCPC_MULTIPLIER=2.5
OBSERVE_HIGHCPC_MIN_SUM=20
OBSERVE_LOWSIMILARITY_SIMILARITY_THRESHOLD=50
OBSERVE_LOWSIMILARITY_COUNT_THRESHOLD=0
OPTIMIZE_LOWPERF_MAX_CLICKS=5
OPTIMIZE_LOWPERF_SIMILARITY_THRESHOLD=40
OPTIMIZE_LOWPERF_COUNT_THRESHOLD=0
```

### 3. 数据库准备

确保PostgreSQL数据库中存在相关度数据表：
```sql
-- 表结构示例
CREATE TABLE pj_similar.product_analyze_similar_result (
    keyword VARCHAR(255),
    target_product_id VARCHAR(50),
    avg_similarity DECIMAL(5,2),
    similar_count INTEGER,
    competitor_count INTEGER,
    valid_scores INTEGER
);
```

## 使用方法

### 1. 测试连接

首先运行连接测试，确保API和数据库连接正常：

```bash
python test_connections.py
```

### 2. 运行分析

执行主程序开始分析：

```bash
python main.py
```

### 3. 查看结果

程序会生成以下文件：
- `keyword_analysis_YYYYMMDD_HHMMSS.csv` - 原始分析数据
- `keyword_analysis_YYYYMMDD_HHMMSS.xlsx` - Excel格式报告
- `keyword_recommendations_YYYYMMDD_HHMMSS.json` - 关键词操作建议
- `analysis_summary_YYYYMMDD_HHMMSS.json` - 统计摘要

## 数据流程

1. **获取广告活动** → 调用 `/adv/v1/promotion/count` API
2. **筛选拍卖广告** → 过滤 type=9 且 status=9 的广告
3. **获取关键词** → 调用 `/adv/v1/stat/words` API
4. **获取统计数据** → 调用 `/adv/v0/stats/keywords` API（按月分段获取）
5. **获取产品信息** → 调用 `/adv/v1/promotion/adverts` API
6. **查询相关度数据** → 从PostgreSQL数据库批量查询
7. **计算分析指标** → 重新计算CTR，新增CPC计算
8. **应用分析规则** → 根据配置的阈值自动标记关键词状态

## 分析规则说明

系统会根据以下规则自动为每个关键词添加分析标记：

### 排除规则（Exclude）

1. **Exclude_Irrelevant（排除不相关）**
   - **条件**: `avg_similarity ≤ 25`
   - **说明**: 平均相似度过低的关键词，与产品相关性不强
   - **配置**: `EXCLUDE_IRRELEVANT_SIMILARITY_THRESHOLD`

2. **Exclude_ZeroClick（排除零点击）**
   - **条件**: `clicks = 0 且 sum ≥ 50`
   - **说明**: 有花费但无点击的关键词，效果差
   - **配置**: `EXCLUDE_ZEROCLICK_MIN_SUM`

3. **Exclude_NoData（排除无数据）**
   - **条件**: `avg_similarity、similar_count、competitor_count、valid_scores 全部为空 且 sum < 20`
   - **说明**: 缺乏相似度数据且花费过低的关键词
   - **配置**: `EXCLUDE_NODATA_MIN_SUM`

### 观察规则（Observe）

4. **Observe_LowCTR（观察低CTR）**
   - **条件**: `views ≥ 500 且 ctr < 0.02`
   - **说明**: 展示量足够但点击率过低的关键词
   - **配置**: `OBSERVE_LOWCTR_MIN_VIEWS`, `OBSERVE_LOWCTR_CTR_THRESHOLD`

5. **Observe_HighCPC（观察高CPC）**
   - **条件**: `sum ≥ 20 且 cpc > 平均CPC × 2.5`
   - **说明**: 花费足够且每次点击成本过高的关键词（排除低花费数据后计算平均CPC）
   - **配置**: `OBSERVE_HIGHCPC_MULTIPLIER`, `OBSERVE_HIGHCPC_MIN_SUM`

6. **Observe_LowSimilarity（观察低相似度）**
   - **条件**: `avg_similarity ≤ 50 且 similar_count ≤ 2`
   - **说明**: 相似度和相似商品数量都较低的关键词
   - **配置**: `OBSERVE_LOWSIMILARITY_SIMILARITY_THRESHOLD`, `OBSERVE_LOWSIMILARITY_COUNT_THRESHOLD`

### 优化规则（Optimize）

7. **Optimize_LowPerf（优化低表现）**
   - **条件**: `clicks < 5 且 avg_similarity ≤ 40 且 similar_count ≤ 0`
   - **说明**: 点击量少且相关性低的关键词，需要优化
   - **配置**: `OPTIMIZE_LOWPERF_MAX_CLICKS`, `OPTIMIZE_LOWPERF_SIMILARITY_THRESHOLD`, `OPTIMIZE_LOWPERF_COUNT_THRESHOLD`

### 保持规则（Keep）

8. **Keep（保持不变）**
   - **条件**: 以上所有标记都为False
   - **说明**: 表现良好的关键词，建议保持现状

9. **是否排除标记（综合判断）**
   - **条件**: 任意一个标记为True
   - **说明**: 用于快速筛选需要处理的关键词

### 规则配置

所有规则阈值都可以通过`.env`文件进行配置，默认值如下：

```env
# 排除规则
EXCLUDE_IRRELEVANT_SIMILARITY_THRESHOLD=25      # 不相关阈值
EXCLUDE_ZEROCLICK_MIN_SUM=50                    # 零点击最小花费
EXCLUDE_NODATA_MIN_SUM=20                       # 无数据最小花费

# 观察规则
OBSERVE_LOWCTR_MIN_VIEWS=500                    # 低CTR最小展示数
OBSERVE_LOWCTR_CTR_THRESHOLD=0.02               # 低CTR阈值
OBSERVE_HIGHCPC_MULTIPLIER=2.5                  # 高CPC倍数
OBSERVE_HIGHCPC_MIN_SUM=20                      # 高CPC最小花费
OBSERVE_LOWSIMILARITY_SIMILARITY_THRESHOLD=50   # 低相似度阈值
OBSERVE_LOWSIMILARITY_COUNT_THRESHOLD=2         # 低相似度数量阈值

# 优化规则
OPTIMIZE_LOWPERF_MAX_CLICKS=5                   # 低表现最大点击数
OPTIMIZE_LOWPERF_SIMILARITY_THRESHOLD=40        # 低表现相似度阈值
OPTIMIZE_LOWPERF_COUNT_THRESHOLD=0              # 低表现数量阈值
```

## 输出文件说明

### CSV/Excel报告字段

| 字段名 | 说明 |
|--------|------|
| campaign_id | 广告活动ID |
| nm_id | 商品ID |
| keyword | 关键词 |
| avg_similarity | 平均相似度 |
| similar_count | 相似商品数量 |
| competitor_count | 竞争对手数量 |
| valid_scores | 有效评分数 |
| views | 展示次数 |
| sum | 花费金额 |
| clicks | 点击次数 |
| ctr | 点击率（重新计算：clicks ÷ views） |
| cpc | 每次点击成本（新增：sum ÷ clicks） |
| count | 关键词热度 |
| Exclude_Irrelevant | 排除不相关（avg_similarity ≤ 25） |
| Exclude_ZeroClick | 排除零点击（clicks = 0 且 sum ≥ 50） |
| Exclude_NoData | 排除无数据（相似度数据全部为空 且 sum < 20） |
| Observe_LowCTR | 观察低CTR（views ≥ 500 且 ctr < 0.02） |
| Observe_HighCPC | 观察高CPC（sum ≥ 20 且 cpc > 平均CPC × 2.5） |
| Observe_LowSimilarity | 观察低相似度（avg_similarity ≤ 50 且 similar_count ≤ 2） |
| Optimize_LowPerf | 优化低表现（clicks < 5 且 avg_similarity ≤ 40 且 similar_count ≤ 0） |
| Keep | 保持不变（以上所有标记都为False） |
| 是否排除标记 | 综合排除标记（任意一个标记为True则为True） |

### JSON建议文件结构

```json
{
  "keep_unchanged": [
    {
      "campaign_id": 12345,
      "keyword": "高效关键词",
      "reason": "高效益关键词 (相似度: 85.0%, CTR: 5.20%)",
      "efficiency_score": 0.756
    }
  ],
  "fix_keywords": [...],
  "exclude_keywords": [...]
}
```

## API限制和注意事项

- **请求频率**: 建议每个请求间隔1-2秒
- **数据范围**: 关键词统计数据最多获取7天，需要分段获取月度数据
- **重试机制**: 内置指数退避重试机制
- **多账号**: 支持多个API密钥轮换使用

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看API请求频率是否过高

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务正常运行
   - 验证用户权限

3. **没有获取到关键词**
   - 确认广告活动状态正确
   - 检查广告活动是否设置了关键词
   - 尝试增加处理的广告活动数量

### 日志文件

程序运行时会在 `logs/` 目录下生成详细的日志文件，可用于问题诊断。

## 开发和扩展

### 添加新的分析指标

1. 在 `models.py` 中扩展数据模型
2. 在 `data_processor.py` 中修改效益计算算法
3. 更新报告生成逻辑

### 自定义分析规则

1. **修改规则阈值**: 在`.env`文件中调整各项规则的阈值参数
2. **添加新规则**: 在`data_processor.py`的`_add_analysis_flags()`方法中添加新的分析逻辑
3. **修改现有规则**: 在`_add_analysis_flags()`方法中修改现有规则的判断条件

### 添加新的数据源

1. 在 `wb_api_client.py` 中添加新的API调用方法
2. 在 `database.py` 中添加新的数据库查询方法
3. 更新数据处理流程

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系
