"""
配置管理模块
"""
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 数据库配置
    pg_host: str = Field(..., env="PG_HOST")
    pg_port: int = Field(5432, env="PG_PORT")
    pg_user: str = Field(..., env="PG_USER")
    pg_password: str = Field(..., env="PG_PASSWORD")
    pg_db: str = Field(..., env="PG_DB")
    
    # Wildberries API配置
    wb_api_keys: str = Field(..., env="WB_API_KEYS")
    wb_adv_api_base_url: str = Field("https://advert-api.wildberries.ru", env="WB_ADV_API_BASE_URL")
    wb_analytics_api_base_url: str = Field("https://seller-analytics-api.wildberries.ru", env="WB_ANALYTICS_API_BASE_URL")
    
    # 应用配置
    log_level: str = Field("INFO", env="LOG_LEVEL")
    max_retries: int = Field(3, env="MAX_RETRIES")
    request_timeout: int = Field(30, env="REQUEST_TIMEOUT")
    data_period_days: int = Field(30, env="DATA_PERIOD_DAYS")
    api_call_interval: float = Field(1.0, env="API_CALL_INTERVAL")

    # 数据分析规则配置
    exclude_irrelevant_similarity_threshold: int = Field(25, env="EXCLUDE_IRRELEVANT_SIMILARITY_THRESHOLD")
    exclude_zeroclick_min_sum: int = Field(50, env="EXCLUDE_ZEROCLICK_MIN_SUM")
    exclude_nodata_min_sum: int = Field(20, env="EXCLUDE_NODATA_MIN_SUM")
    observe_lowctr_min_views: int = Field(500, env="OBSERVE_LOWCTR_MIN_VIEWS")
    observe_lowctr_ctr_threshold: float = Field(0.02, env="OBSERVE_LOWCTR_CTR_THRESHOLD")
    observe_highcpc_multiplier: float = Field(2.5, env="OBSERVE_HIGHCPC_MULTIPLIER")
    observe_highcpc_min_sum: int = Field(20, env="OBSERVE_HIGHCPC_MIN_SUM")
    observe_lowsimilarity_similarity_threshold: int = Field(50, env="OBSERVE_LOWSIMILARITY_SIMILARITY_THRESHOLD")
    observe_lowsimilarity_count_threshold: int = Field(2, env="OBSERVE_LOWSIMILARITY_COUNT_THRESHOLD")
    optimize_lowperf_max_clicks: int = Field(5, env="OPTIMIZE_LOWPERF_MAX_CLICKS")
    optimize_lowperf_similarity_threshold: int = Field(40, env="OPTIMIZE_LOWPERF_SIMILARITY_THRESHOLD")
    optimize_lowperf_count_threshold: int = Field(0, env="OPTIMIZE_LOWPERF_COUNT_THRESHOLD")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @property
    def api_keys_list(self) -> List[str]:
        """获取API密钥列表"""
        return [key.strip() for key in self.wb_api_keys.split(",") if key.strip()]
    
    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.pg_user}:{self.pg_password}@{self.pg_host}:{self.pg_port}/{self.pg_db}"


# 全局配置实例
settings = Settings()
