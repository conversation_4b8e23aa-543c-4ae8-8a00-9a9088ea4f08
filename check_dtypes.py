"""
检查DataFrame数据类型的脚本
"""
import pandas as pd
from loguru import logger
from main import WildberriesKeywordAnalyzer

def check_data_types():
    """检查数据类型"""
    logger.info("开始检查数据类型...")
    
    try:
        # 创建分析器
        analyzer = WildberriesKeywordAnalyzer()
        
        # 检查数据库连接
        if not analyzer._check_database():
            logger.error("数据库连接失败")
            return
        
        # 获取广告活动
        campaigns = analyzer._get_campaigns()
        if not campaigns:
            logger.error("没有获取到广告活动")
            return
        
        # 筛选拍卖广告
        auction_campaigns = analyzer._filter_auction_campaigns(campaigns)
        if not auction_campaigns:
            logger.error("没有找到拍卖广告")
            return
        
        # 只测试第一个广告活动
        test_campaign = auction_campaigns[0]
        logger.info(f"测试广告活动: {test_campaign.campaign_id}")
        
        # 使用第一个API客户端进行分析
        if not analyzer.api_manager.clients:
            logger.error("没有可用的API客户端")
            return
        
        from data_processor import KeywordAnalyzer
        keyword_analyzer = KeywordAnalyzer(analyzer.api_manager.clients[0])
        
        # 分析单个广告活动
        result_df = keyword_analyzer.analyze_campaign(test_campaign)
        
        if result_df.empty:
            logger.warning("分析结果为空")
            return
        
        logger.info("=== DataFrame中的数据类型 ===")
        for col in result_df.columns:
            dtype = result_df[col].dtype
            logger.info(f"{col}: {dtype}")
        
        logger.info("=== 检查整数字段的实际值 ===")
        integer_fields = ['avg_similarity', 'similar_count', 'competitor_count', 'valid_scores', 
                         'views', 'sum', 'clicks', 'count']
        
        for field in integer_fields:
            if field in result_df.columns:
                sample_values = result_df[field].dropna().head(3).tolist()
                logger.info(f"{field} 样本值: {sample_values}")
        
        logger.info("=== CTR字段精度检查 ===")
        ctr_sample = result_df['ctr'].head(5).tolist()
        logger.info(f"CTR 样本值: {ctr_sample}")
        
        # 保存为Excel以保持数据类型
        try:
            result_df.to_excel("test_result_with_types.xlsx", index=False)
            logger.info("✅ Excel文件已保存，数据类型得到保持")
        except ImportError:
            logger.warning("未安装openpyxl，跳过Excel保存")
        
        return result_df
        
    except Exception as e:
        logger.error(f"检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    check_data_types()
