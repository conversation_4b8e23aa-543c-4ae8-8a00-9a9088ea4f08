"""
测试search-texts API接口，查看返回的text列表
"""
import os
import sys
import json
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wb_api_client import WildberriesAPIClient
from config import settings


def test_search_texts_api_detailed():
    """详细测试search-texts API接口"""
    logger.info("=== 详细测试search-texts API接口 ===")
    
    try:
        # 检查API密钥配置
        if not hasattr(settings, 'api_keys_list') or not settings.api_keys_list:
            logger.error("未配置API密钥，无法进行真实API测试")
            logger.info("请在.env文件中配置API_KEYS")
            return False
        
        # 创建API客户端
        api_key = settings.api_keys_list[0]
        client = WildberriesAPIClient(api_key)
        logger.info(f"使用API密钥: {api_key[:10]}...")
        
        # 测试产品ID（这些是API文档中的示例ID）
        test_nm_ids = [162579635, 166699779]
        logger.info(f"测试产品ID: {test_nm_ids}")
        
        # 调用API
        logger.info("正在调用search-texts API...")
        logger.info("注意：此API有请求限制，每分钟3个请求，间隔20秒")
        
        search_texts = client.get_search_texts_by_products(test_nm_ids)
        
        # 详细展示结果
        logger.info("=== API返回结果分析 ===")
        logger.info(f"返回的搜索文本总数: {len(search_texts)}")
        
        if search_texts:
            logger.info("=== 搜索文本列表 ===")
            for i, text in enumerate(search_texts, 1):
                logger.info(f"{i:2d}. {text}")
            
            # 分析文本特征
            logger.info("=== 文本特征分析 ===")
            logger.info(f"最短文本长度: {min(len(text) for text in search_texts)}")
            logger.info(f"最长文本长度: {max(len(text) for text in search_texts)}")
            logger.info(f"平均文本长度: {sum(len(text) for text in search_texts) / len(search_texts):.1f}")
            
            # 显示一些示例
            logger.info("=== 文本示例 ===")
            for i, text in enumerate(search_texts[:5]):  # 只显示前5个
                logger.info(f"示例 {i+1}: '{text}' (长度: {len(text)})")
        else:
            logger.warning("API返回了空的搜索文本列表")
        
        return True
        
    except Exception as e:
        logger.error(f"测试search-texts API失败: {e}")
        return False


def test_api_request_format():
    """展示API请求格式"""
    logger.info("=== API请求格式展示 ===")
    
    from datetime import datetime, timedelta
    
    # 计算日期范围（与实际API调用相同的逻辑）
    end_date = datetime.now().date() - timedelta(days=1)  # 昨天
    start_date = end_date - timedelta(days=30)  # 一个月前
    
    # 过去时期（用于对比）
    past_end_date = start_date - timedelta(days=1)
    past_start_date = past_end_date - timedelta(days=30)
    
    # 构建请求payload
    payload = {
        "currentPeriod": {
            "start": start_date.strftime('%Y-%m-%d'),
            "end": end_date.strftime('%Y-%m-%d')
        },
        "pastPeriod": {
            "start": past_start_date.strftime('%Y-%m-%d'),
            "end": past_end_date.strftime('%Y-%m-%d')
        },
        "nmIds": [162579635, 166699779],  # 示例产品ID
        "topOrderBy": "orders",
        "includeSubstitutedSKUs": True,
        "includeSearchTexts": True,
        "orderBy": {
            "field": "orders",
            "mode": "desc"
        },
        "limit": 30
    }
    
    logger.info("API请求地址:")
    logger.info("POST https://seller-analytics-api.wildberries.ru/api/v2/search-report/product/search-texts")
    
    logger.info("请求头:")
    logger.info("Authorization: <API_KEY>")
    logger.info("Content-Type: application/json")
    
    logger.info("请求体:")
    logger.info(json.dumps(payload, indent=2, ensure_ascii=False))
    
    logger.info("预期响应格式:")
    expected_response = {
        "data": {
            "items": [
                {
                    "text": "костюм",
                    "nmId": 211131895,
                    "subjectName": "Phones",
                    "brandName": "Apple",
                    "vendorCode": "wb3ha2668w",
                    "name": "iPhone 13 256 ГБ Серебристый",
                    # ... 其他字段
                }
            ]
        }
    }
    logger.info(json.dumps(expected_response, indent=2, ensure_ascii=False))


def test_with_real_campaign_data():
    """使用真实广告活动数据测试"""
    logger.info("=== 使用真实广告活动数据测试 ===")
    
    try:
        # 检查API密钥配置
        if not hasattr(settings, 'api_keys_list') or not settings.api_keys_list:
            logger.warning("未配置API密钥，跳过真实数据测试")
            return True
        
        # 创建API客户端
        api_key = settings.api_keys_list[0]
        client = WildberriesAPIClient(api_key)
        
        # 获取一个真实的广告活动
        logger.info("获取真实广告活动...")
        campaigns = client.get_campaigns()
        
        if not campaigns:
            logger.warning("未找到广告活动")
            return True
        
        # 筛选拍卖广告
        auction_campaigns = client.filter_auction_campaigns(campaigns)
        
        if not auction_campaigns:
            logger.warning("未找到拍卖广告")
            return True
        
        # 使用第一个广告活动
        campaign = auction_campaigns[0]
        logger.info(f"使用广告活动: {campaign.campaign_id} - {campaign.name}")
        
        # 获取产品列表
        products = client.get_campaign_products(campaign.campaign_id)
        
        if not products:
            logger.warning("该广告活动没有产品")
            return True
        
        logger.info(f"广告活动有 {len(products)} 个产品")
        
        # 获取前几个产品的搜索文本
        nm_ids = [product.nm_id for product in products[:3]]  # 只测试前3个产品
        logger.info(f"测试产品ID: {nm_ids}")
        
        # 调用search-texts API
        search_texts = client.get_search_texts_by_products(nm_ids)
        
        logger.info("=== 真实数据结果 ===")
        logger.info(f"获取到 {len(search_texts)} 个搜索文本")
        
        if search_texts:
            logger.info("搜索文本列表:")
            for i, text in enumerate(search_texts[:10], 1):  # 只显示前10个
                logger.info(f"{i:2d}. {text}")
        
        return True
        
    except Exception as e:
        logger.error(f"真实数据测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试search-texts API接口...")
    
    # 1. 展示API请求格式
    test_api_request_format()
    
    print("\n" + "="*60 + "\n")
    
    # 2. 详细测试API接口
    test_search_texts_api_detailed()
    
    print("\n" + "="*60 + "\n")
    
    # 3. 使用真实广告活动数据测试
    test_with_real_campaign_data()
    
    logger.info("测试完成")


if __name__ == "__main__":
    main()
