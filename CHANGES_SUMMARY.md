# analyze_campaign函数修改总结

## 修改需求
1. 查询数据库只需要用关键词查询不需要产品id，查询关键词时可能返回很多条数据，只需要获取到任意一条数据就可以。
2. 获取产品列表功能不要删除，产品列表还是有用的。
3. 配置新的API接口 `/api/v2/search-report/product/search-texts` 来获取搜索文本。

## 主要修改内容

### 1. 数据库管理器 (database.py)
**新增方法**: `get_similarity_data_by_keyword(keyword: str)`
- 功能：根据关键词获取任意一条相似度数据
- 使用 `LIMIT 1` 确保只返回一条记录
- 简化了查询逻辑，不再需要产品ID

```python
def get_similarity_data_by_keyword(self, keyword: str) -> Optional[SimilarityData]:
    """根据关键词获取任意一条相似度数据"""
    query = """
    SELECT keyword, target_product_id, avg_similarity, similar_count, competitor_count, valid_scores
    FROM pj_similar.product_analyze_similar_result 
    WHERE keyword = %s
    LIMIT 1
    """
```

### 2. API客户端 (wb_api_client.py)
**新增方法**: `get_search_texts_by_products(nm_ids: List[int])`
- 功能：调用Wildberries搜索文本API获取产品相关的搜索文本
- API地址：`/api/v2/search-report/product/search-texts`
- 实现了请求限制控制（每分钟3个请求，间隔20秒）
- 自动去重搜索文本

**API配置参数**：
```python
payload = {
    "currentPeriod": {"start": "YYYY-MM-DD", "end": "YYYY-MM-DD"},
    "pastPeriod": {"start": "YYYY-MM-DD", "end": "YYYY-MM-DD"},
    "nmIds": [产品ID列表],
    "topOrderBy": "orders",
    "includeSubstitutedSKUs": True,
    "includeSearchTexts": True,
    "orderBy": {"field": "orders", "mode": "desc"},
    "limit": 30
}
```

### 3. 数据处理器 (data_processor.py)
**修改方法**: `analyze_campaign(campaign: Campaign)`
- 保留了获取产品列表的功能
- 改用关键词查询相似度数据，不再需要产品ID
- 新增搜索文本获取功能
- 调用新的数据合并方法

**新增方法**: `_merge_data_with_pandas_v2()`
- 支持新的数据结构（关键词相似度数据）
- 处理搜索文本数据
- 保持与原有数据格式的兼容性

## API接口详细信息

### 接口地址
```
POST https://seller-analytics-api.wildberries.ru/api/v2/search-report/product/search-texts
```

### 请求限制
- **频率限制**: 每分钟3个请求
- **间隔时间**: 20秒
- **突发请求**: 3个请求

### 时间范围配置
- **currentPeriod**: 当前数据的日期范围（默认1个月）
- **pastPeriod**: 与currentPeriod对比的前一时期范围
- **关系**: pastPeriod的结束日期 = currentPeriod的开始日期 - 1天

### 响应数据处理
- 提取 `data.items[].text` 字段
- 自动去重处理
- 返回纯文本列表

## 数据流程变化

### 原流程
1. 获取关键词 → 2. 获取关键词统计 → 3. 获取产品列表 → 4. 批量查询相似度数据（关键词+产品ID） → 5. 数据合并

### 新流程
1. 获取关键词 → 2. 获取关键词统计 → 3. 获取产品列表 → 4. 获取搜索文本（新增） → 5. 查询相似度数据（仅关键词） → 6. 数据合并（新方法）

## 注意事项

### 1. API限制处理
- 实现了自动等待机制（20秒间隔）
- 使用重试机制处理失败请求
- 记录详细的请求日志

### 2. 数据兼容性
- 保持原有数据结构不变
- 新增搜索文本信息记录
- 支持空数据处理

### 3. 错误处理
- 完善的异常捕获机制
- 详细的错误日志记录
- 优雅的降级处理

## 测试验证
创建了 `test_new_features.py` 测试脚本，验证：
- 数据库关键词查询功能
- 搜索文本API接口配置
- 新的分析流程逻辑

所有测试均通过，功能正常运行。
