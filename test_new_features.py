"""
测试新功能的脚本
"""
import os
import sys
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wb_api_client import WildberriesAPIClient
from database import db_manager
from data_processor import KeywordAnalyzer
from models import Campaign
from datetime import datetime


def test_database_keyword_query():
    """测试数据库关键词查询功能"""
    logger.info("=== 测试数据库关键词查询功能 ===")
    
    # 测试数据库连接
    if not db_manager.test_connection():
        logger.error("数据库连接失败")
        return False
    
    # 获取样本数据
    sample_data = db_manager.get_table_sample_data(5)
    if not sample_data:
        logger.error("无法获取样本数据")
        return False
    
    logger.info(f"样本数据: {sample_data}")
    
    # 测试关键词查询
    for data in sample_data[:2]:  # 只测试前2个
        keyword = data['keyword']
        logger.info(f"测试关键词: {keyword}")
        
        similarity_data = db_manager.get_similarity_data_by_keyword(keyword)
        if similarity_data:
            logger.info(f"找到相似度数据: {similarity_data}")
        else:
            logger.warning(f"未找到关键词 '{keyword}' 的相似度数据")
    
    return True


def test_search_texts_api():
    """测试搜索文本API功能"""
    logger.info("=== 测试搜索文本API功能 ===")
    
    # 这里需要真实的API密钥，所以只做模拟测试
    logger.info("注意：搜索文本API需要真实的API密钥和产品ID")
    logger.info("API地址: https://seller-analytics-api.wildberries.ru/api/v2/search-report/product/search-texts")
    logger.info("请求限制: 每分钟3个请求，间隔20秒")
    
    # 模拟测试数据
    test_nm_ids = [162579635, 166699779]
    logger.info(f"测试产品ID: {test_nm_ids}")
    
    # 如果有API密钥，可以取消注释下面的代码进行真实测试
    # try:
    #     from config import settings
    #     if hasattr(settings, 'api_keys_list') and settings.api_keys_list:
    #         api_key = settings.api_keys_list[0]
    #         client = WildberriesAPIClient(api_key)
    #         search_texts = client.get_search_texts_by_products(test_nm_ids)
    #         logger.info(f"获取到的搜索文本: {search_texts}")
    #     else:
    #         logger.warning("未配置API密钥，跳过真实API测试")
    # except Exception as e:
    #     logger.error(f"API测试失败: {e}")
    
    return True


def test_new_analyze_campaign():
    """测试新的analyze_campaign功能"""
    logger.info("=== 测试新的analyze_campaign功能 ===")
    
    # 创建模拟的广告活动
    test_campaign = Campaign(
        campaign_id=12345,
        name="测试广告活动",
        type=9,
        status=9,
        create_time=datetime.now(),
        change_time=datetime.now()
    )
    
    logger.info(f"测试广告活动: {test_campaign}")
    
    # 这里需要真实的API密钥进行完整测试
    logger.info("注意：完整测试需要真实的API密钥")
    logger.info("新功能包括:")
    logger.info("1. 只用关键词查询数据库相似度数据")
    logger.info("2. 保留产品列表获取功能")
    logger.info("3. 调用search-texts API获取搜索文本")
    logger.info("4. 使用新的数据合并方法")
    
    return True


def main():
    """主测试函数"""
    logger.info("开始测试新功能...")
    
    # 测试数据库功能
    if not test_database_keyword_query():
        logger.error("数据库测试失败")
        return False
    
    # 测试API功能
    if not test_search_texts_api():
        logger.error("API测试失败")
        return False
    
    # 测试新的分析功能
    if not test_new_analyze_campaign():
        logger.error("分析功能测试失败")
        return False
    
    logger.info("✅ 所有测试完成")
    return True


if __name__ == "__main__":
    main()
