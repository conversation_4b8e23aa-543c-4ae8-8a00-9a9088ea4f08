# ==================== 数据库配置 ====================
# 选项1：使用主服务的PostgreSQL
# PG_HOST=postgres
# PG_PORT=5432
# PG_USER=admin
# PG_PASSWORD=admin123
# PG_DB=microservices

# 选项2：使用外部数据库
PG_HOST=************
PG_PORT=5432
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# ==================== Wildberries API配置 ====================
# 多个API密钥用逗号分隔
WB_API_KEYS=your_api_key_1,your_api_key_2,your_api_key_3

# API基础URL
WB_ADV_API_BASE_URL=https://advert-api.wildberries.ru
WB_ANALYTICS_API_BASE_URL=https://seller-analytics-api.wildberries.ru

# ==================== 应用配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 请求重试次数
MAX_RETRIES=3

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# 数据获取时间段（天）
DATA_PERIOD_DAYS=30

# 每次API调用间隔（秒）
API_CALL_INTERVAL=1

# ==================== 数据分析规则配置 ====================
# Exclude_Irrelevant规则：平均相似度阈值
EXCLUDE_IRRELEVANT_SIMILARITY_THRESHOLD=25

# Exclude_ZeroClick规则：最小花费阈值
EXCLUDE_ZEROCLICK_MIN_SUM=50

# Exclude_NoData规则：无相似度数据时的最小花费阈值
EXCLUDE_NODATA_MIN_SUM=20

# Observe_LowCTR规则：最小展示次数和CTR阈值
OBSERVE_LOWCTR_MIN_VIEWS=500
OBSERVE_LOWCTR_CTR_THRESHOLD=0.02

# Observe_HighCPC规则：CPC倍数阈值和最小花费阈值
OBSERVE_HIGHCPC_MULTIPLIER=2.5
OBSERVE_HIGHCPC_MIN_SUM=20

# Observe_LowSimilarity规则：相似度和相似数量阈值
OBSERVE_LOWSIMILARITY_SIMILARITY_THRESHOLD=50
OBSERVE_LOWSIMILARITY_COUNT_THRESHOLD=2

# Optimize_LowPerf规则：点击数、相似度和相似数量阈值
OPTIMIZE_LOWPERF_MAX_CLICKS=5
OPTIMIZE_LOWPERF_SIMILARITY_THRESHOLD=40
OPTIMIZE_LOWPERF_COUNT_THRESHOLD=0
