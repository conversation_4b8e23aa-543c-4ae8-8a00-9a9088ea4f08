"""
Wildberries广告关键词效益分析主程序
"""
import json
import pandas as pd
from datetime import datetime,timedelta
from typing import List, Dict, Any, Optional
from loguru import logger

from database import db_manager
from wb_api_client import MultiAccountAPIManager
from data_processor import KeywordAnalyzer
from models import Campaign, FinalResult


class WildberriesKeywordAnalyzer:
    """Wildberries关键词分析器主类"""

    def __init__(self):
        self.api_manager = MultiAccountAPIManager()
        self.results = pd.DataFrame()
    
    def run_analysis(self, limit_campaigns: Optional[int] = None) -> bool:
        """运行完整的分析流程"""
        logger.info("=" * 60)
        logger.info("开始Wildberries广告关键词效益分析")
        logger.info("=" * 60)
        
        try:
            # 1. 检查数据库连接
            if not self._check_database():
                return False
            
            # 2. 获取所有广告活动
            all_campaigns = self._get_campaigns()
            if not all_campaigns:
                logger.error("没有获取到任何广告活动")
                return False
            
            # 3. 筛选拍卖广告
            auction_campaigns = self._filter_auction_campaigns(all_campaigns)
            if not auction_campaigns:
                logger.error("没有找到符合条件的拍卖广告")
                return False
            
            # 4. 限制处理数量（用于测试）
            if limit_campaigns:
                auction_campaigns = auction_campaigns[:limit_campaigns]
                logger.info(f"限制处理前 {limit_campaigns} 个广告活动")
            
            # 5. 分析广告活动
            self.results = self._analyze_campaigns(auction_campaigns)
            if self.results.empty:
                logger.error("分析结果为空")
                return False
            
            # 6. 生成报告
            self._generate_reports()
            
            logger.info("=" * 60)
            logger.info("分析完成！")
            logger.info("=" * 60)
            return True
            
        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            return False
    
    def _check_database(self) -> bool:
        """检查数据库连接"""
        logger.info("检查数据库连接...")
        
        if not db_manager.test_connection():
            logger.error("数据库连接失败")
            return False
        
        if not db_manager.check_table_exists():
            logger.error("相关度数据表不存在")
            return False
        
        logger.info("✅ 数据库连接正常")
        return True
    
    def _get_campaigns(self) -> List[Campaign]:
        """获取所有广告活动"""
        logger.info("获取所有账号的广告活动...")
        
        campaigns = self.api_manager.get_all_campaigns()
        logger.info(f"✅ 总共获取到 {len(campaigns)} 个广告活动")
        
        return campaigns
    
    def _filter_auction_campaigns(self, campaigns: List[Campaign]) -> List[Campaign]:
        """筛选拍卖广告"""
        logger.info("筛选拍卖广告 (type=9, status=9)...")
        
        auction_campaigns = []
        for campaign in campaigns:
            # # 排除创建时间不到1个月的广告
            # cutoff_date = datetime.now() - timedelta(days=30)
            # # 将campaign.create_time转换为naive datetime进行比较
            # campaign_time = campaign.create_time.replace(tzinfo=None) if campaign.create_time.tzinfo else campaign.create_time
            # if campaign_time > cutoff_date:
            #     logger.info(f"  排除拍卖广告: ID={campaign.campaign_id}, 名称='{campaign.name}', 因为创建不到一个月，其创建时间={campaign.create_time}")
            #     continue

            if campaign.type == 9 and campaign.status == 9 and campaign.campaign_id not in [26973370,22590989]:
                auction_campaigns.append(campaign)
        
        logger.info(f"✅ 筛选出 {len(auction_campaigns)} 个拍卖广告")
        
        # 显示前几个广告活动信息
        for i, campaign in enumerate(auction_campaigns[:5], 1):
            logger.info(f"  拍卖广告 {i}: ID={campaign.campaign_id}, 名称='{campaign.name}', 创建时间={campaign.create_time}")
        
        return auction_campaigns
    
    def _analyze_campaigns(self, campaigns: List[Campaign]):
        """分析广告活动"""
        logger.info(f"开始分析 {len(campaigns)} 个拍卖广告...")

        all_dataframes = []

        # 使用第一个API客户端进行分析
        if not self.api_manager.clients:
            logger.error("没有可用的API客户端")
            return pd.DataFrame()

        analyzer = KeywordAnalyzer(self.api_manager.clients[0])

        for i, campaign in enumerate(campaigns, 1):
            logger.info(f"处理第 {i}/{len(campaigns)} 个广告活动: {campaign.campaign_id}")

            try:
                df_result = analyzer.analyze_campaign(campaign)
                if not df_result.empty:
                    all_dataframes.append(df_result)
                    logger.info(f"  ✅ 广告活动 {campaign.campaign_id} 生成 {len(df_result)} 条结果")
                else:
                    logger.info(f"  ⚠️ 广告活动 {campaign.campaign_id} 没有生成结果")

            except Exception as e:
                logger.error(f"  ❌ 处理广告活动 {campaign.campaign_id} 失败: {e}")
                continue

        # 合并所有DataFrame
        if all_dataframes:
            final_df = pd.concat(all_dataframes, ignore_index=True)
            logger.info(f"✅ 分析完成，总共生成 {len(final_df)} 条结果")
            return final_df
        else:
            logger.warning("没有生成任何结果")
            return pd.DataFrame()
    
    def _generate_reports(self):
        """生成报告 - 每个广告组一个独立报告"""
        logger.info("生成分析报告...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if self.results.empty:
            logger.warning("没有数据可生成报告")
            return

        # 按广告组分组生成报告
        campaign_groups = self.results.groupby('campaign_id')
        total_campaigns = len(campaign_groups)

        logger.info(f"为 {total_campaigns} 个广告组生成独立报告...")

        for campaign_id, campaign_data in campaign_groups:
            try:
                # 生成单个广告组的报告
                campaign_id_int = int(str(campaign_id))  # 安全转换
                self._generate_campaign_report(campaign_id_int, campaign_data, timestamp)
            except Exception as e:
                logger.error(f"生成广告组 {campaign_id} 报告失败: {e}")

        # 生成汇总报告
        self._generate_summary_report(timestamp)

        logger.info(f"✅ 已为 {total_campaigns} 个广告组生成独立报告")

    def _generate_campaign_report(self, campaign_id: int, campaign_data: pd.DataFrame, timestamp: str):
        """为单个广告组生成报告"""
        logger.info(f"生成广告组 {campaign_id} 的报告...")

        # 获取广告组基本信息
        keywords_count = len(campaign_data)

        # 生成文件名
        csv_filename = f"campaign_{campaign_id}_analysis_{timestamp}.csv"
        excel_filename = f"campaign_{campaign_id}_analysis_{timestamp}.xlsx"

        # 导出CSV
        campaign_data.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        logger.info(f"  ✅ CSV: {csv_filename} ({keywords_count} 条记录)")

        # 导出Excel
        try:
            campaign_data.to_excel(excel_filename, index=False)
            logger.info(f"  ✅ Excel: {excel_filename}")
        except ImportError:
            logger.warning(f"  ⚠️ 跳过Excel导出 (未安装openpyxl): {excel_filename}")

        # 生成广告组统计摘要
        self._generate_campaign_summary(campaign_id, campaign_data, timestamp)

    def _generate_campaign_summary(self, campaign_id: int, campaign_data: pd.DataFrame, timestamp: str):
        """生成单个广告组的统计摘要"""

        # 计算统计数据
        total_keywords = campaign_data['keyword'].nunique()
        total_products = campaign_data['nm_id'].nunique()

        total_views = campaign_data['views'].sum()
        total_clicks = campaign_data['clicks'].sum()
        total_spend = campaign_data['sum'].sum()

        avg_ctr = campaign_data['ctr'].mean()
        avg_cpc = campaign_data['cpc'].mean()
        avg_similarity = campaign_data['avg_similarity'].mean()

        # 分析标记统计
        flag_columns = ['Order_Generating', 'Exclude_Irrelevant', 'Exclude_ZeroClick', 'Exclude_NoData',
                       'Observe_LowCTR', 'Observe_HighCPC', 'Observe_LowSimilarity',
                       'Optimize_LowPerf', 'Keep', '是否排除标记']

        flag_stats = {}
        for col in flag_columns:
            if col in campaign_data.columns:
                true_count = campaign_data[col].sum()
                total_count = len(campaign_data)
                percentage = (true_count / total_count * 100) if total_count > 0 else 0
                flag_stats[col] = {
                    "count": int(true_count),
                    "total": total_count,
                    "percentage": round(percentage, 1)
                }

        summary = {
            "campaign_id": campaign_id,
            "analysis_time": datetime.now().isoformat(),
            "total_records": len(campaign_data),
            "total_keywords": total_keywords,
            "total_products": total_products,
            "total_views": int(total_views) if pd.notna(total_views) else 0,
            "total_clicks": int(total_clicks) if pd.notna(total_clicks) else 0,
            "total_spend": float(total_spend) if pd.notna(total_spend) else 0.0,
            "average_ctr": float(avg_ctr) if pd.notna(avg_ctr) else 0.0,
            "average_cpc": float(avg_cpc) if pd.notna(avg_cpc) else 0.0,
            "average_similarity": float(avg_similarity) if pd.notna(avg_similarity) else 0.0,
            "flag_statistics": flag_stats
        }

        # 保存摘要文件
        summary_filename = f"campaign_{campaign_id}_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logger.info(f"  ✅ 摘要: {summary_filename}")

        # 打印关键统计
        logger.info(f"  广告组 {campaign_id} 统计:")
        logger.info(f"    关键词: {total_keywords} 个")
        logger.info(f"    产品: {total_products} 个")
        logger.info(f"    展示: {summary['total_views']:,}")
        logger.info(f"    点击: {summary['total_clicks']:,}")
        logger.info(f"    花费: {summary['total_spend']:.2f} 元")
        logger.info(f"    平均CTR: {summary['average_ctr']:.3f}")
        logger.info(f"    平均CPC: {summary['average_cpc']:.2f}")
        if summary['average_similarity'] > 0:
            logger.info(f"    平均相似度: {summary['average_similarity']:.1f}%")

    def _generate_summary_report(self, timestamp: str):
        """生成全局汇总报告"""
        logger.info("生成全局汇总报告...")

        if self.results.empty:
            return

        # 计算统计数据
        total_campaigns = self.results['campaign_id'].nunique()
        total_keywords = self.results['keyword'].nunique()
        total_products = self.results['nm_id'].nunique()

        total_views = self.results['views'].sum()
        total_clicks = self.results['clicks'].sum()
        total_spend = self.results['sum'].sum()

        avg_ctr = self.results['ctr'].mean()
        avg_cpc = self.results['cpc'].mean()
        avg_similarity = self.results['avg_similarity'].mean()

        # 全局分析标记统计
        flag_columns = ['Order_Generating', 'Exclude_Irrelevant', 'Exclude_ZeroClick', 'Exclude_NoData',
                       'Observe_LowCTR', 'Observe_HighCPC', 'Observe_LowSimilarity',
                       'Optimize_LowPerf', 'Keep', '是否排除标记']

        global_flag_stats = {}
        for col in flag_columns:
            if col in self.results.columns:
                true_count = self.results[col].sum()
                total_count = len(self.results)
                percentage = (true_count / total_count * 100) if total_count > 0 else 0
                global_flag_stats[col] = {
                    "count": int(true_count),
                    "total": total_count,
                    "percentage": round(percentage, 1)
                }

        summary = {
            "analysis_time": datetime.now().isoformat(),
            "total_records": len(self.results),
            "total_campaigns": total_campaigns,
            "total_keywords": total_keywords,
            "total_products": total_products,
            "total_views": int(total_views) if pd.notna(total_views) else 0,
            "total_clicks": int(total_clicks) if pd.notna(total_clicks) else 0,
            "total_spend": float(total_spend) if pd.notna(total_spend) else 0.0,
            "average_ctr": float(avg_ctr) if pd.notna(avg_ctr) else 0.0,
            "average_cpc": float(avg_cpc) if pd.notna(avg_cpc) else 0.0,
            "average_similarity": float(avg_similarity) if pd.notna(avg_similarity) else 0.0,
            "global_flag_statistics": global_flag_stats
        }

        # 保存全局汇总文件
        summary_filename = f"global_analysis_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ 全局汇总已保存到 {summary_filename}")

        # 打印关键统计
        logger.info("全局分析统计摘要:")
        logger.info(f"  总记录数: {len(self.results)}")
        logger.info(f"  涉及广告活动: {total_campaigns} 个")
        logger.info(f"  涉及关键词: {total_keywords} 个")
        logger.info(f"  涉及产品: {total_products} 个")
        logger.info(f"  总展示次数: {summary['total_views']:,}")
        logger.info(f"  总点击次数: {summary['total_clicks']:,}")
        logger.info(f"  总花费: {summary['total_spend']:.2f} 元")
        logger.info(f"  平均点击率: {summary['average_ctr']:.3f}")
        logger.info(f"  平均CPC: {summary['average_cpc']:.2f}")
        if summary['average_similarity'] > 0:
            logger.info(f"  平均相似度: {summary['average_similarity']:.1f}%")
    




def main():
    """主函数"""
    analyzer = WildberriesKeywordAnalyzer()

    # 运行分析（处理所有拍卖广告以找到有关键词的）
    success = analyzer.run_analysis()

    if success:
        logger.info("🎉 分析成功完成！")

if __name__ == "__main__":
    main()
